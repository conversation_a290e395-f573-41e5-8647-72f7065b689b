{"displayName": "Augment Usage Tracker", "description": "Track and display Augment AI usage statistics in VSCode status bar", "command.resetUsage": "Reset Usage Statistics", "command.openSettings": "Open Settings", "command.showDetails": "Show Usage Details", "command.setupCookies": "Setup Browser Cookies", "command.checkAuthStatus": "Check Authentication Status", "command.webLogin": "🌐 Web Login (Auto)", "command.manualRefresh": "🔄 Manual Refresh", "command.setLanguage": "🌐 Set Language", "command.checkCookieStatus": "🍪 Check Cookie Status", "command.refreshCookie": "🔄 Refresh <PERSON><PERSON>", "command.logout": "🚪 Logout", "command.autoRegister": "🤖 Auto Register Account", "command.puppeteerLogin": "🎭 Puppeteer Login & Extract Cookies", "config.title": "Augment Usage Tracker", "config.enabled": "Enable/disable the Augment usage tracker", "config.usageLimit": "Monthly usage limit for tracking", "config.refreshInterval": "Status bar refresh interval in seconds", "config.showInStatusBar": "Show usage statistics in status bar", "config.cookies": "Augment browser session cookies", "config.language": "Interface language setting", "config.language.auto": "Auto (Follow VSCode)", "config.language.en": "English", "config.language.zhCn": "Simplified Chinese", "status.noAuth": "No authentication available for real data fetch", "status.fetchingData": "Fetching real usage data...", "status.apiSuccess": "API connection successful!", "status.apiFailed": "API connection failed", "status.cookiesConfigured": "Augment cookies configured successfully!", "status.checkingAuth": "Checking authentication status...", "status.authStatus": "Authentication Status:", "status.apiToken": "API Token", "status.browserCookies": "Browser Cookies", "status.configured": "Configured", "status.notConfigured": "Not configured", "status.connectionTest": "Connection test", "status.success": "Success", "status.failed": "Failed", "status.error": "Error", "status.suggestion": "Suggestion: Cookies may have expired, please get new ones", "status.pleaseConfigureAuth": "Please configure authentication first", "dialog.browserOpened": "Browser opened! Please login to Augment, then use \"Setup Browser Cookies\" command.", "dialog.setupCookies": "Setup Cookies", "dialog.cancel": "Cancel", "dialog.webLoginError": "Web login error", "usage.currentUsage": "Current Usage", "usage.monthlyLimit": "Monthly Limit", "usage.usagePercentage": "Usage Percentage", "usage.remaining": "Remaining", "usage.lastReset": "Last Reset", "usage.resetUsage": "Reset Usage", "usage.openSettings": "Open Settings", "tooltip.augmentUsageTracker": "Augment Usage Tracker", "tooltip.current": "Current", "tooltip.limit": "Limit", "tooltip.usage": "Usage", "tooltip.remaining": "Remaining", "tooltip.plan": "Plan", "tooltip.dataSource": "Data Source", "tooltip.realDataFromApi": "Real data from Augment API", "tooltip.simulatedData": "Simulated data", "autoRegister.title": "Auto Register Augment Account", "autoRegister.emailPrompt": "Please enter registration email address", "autoRegister.emailPlaceholder": "<EMAIL>", "autoRegister.emailValidation": "Please enter a valid email address", "autoRegister.captchaMode": "Select captcha handling method", "autoRegister.smartWait": "🤖 Smart Wait Mode", "autoRegister.smartWaitDesc": "Automatically detect verification completion", "autoRegister.smartWaitDetail": "Recommended: Auto monitor page changes, continue after verification", "autoRegister.interactive": "👤 Interactive Mode", "autoRegister.interactiveDesc": "Prompt user confirmation at key steps", "autoRegister.interactiveDetail": "Safe: Prompt user confirmation before each step", "autoRegister.manual": "⏸️ Manual Mode", "autoRegister.manualDesc": "Pause and wait for manual completion", "autoRegister.manualDetail": "Simple: Pause when verification detected, continue after completion", "autoRegister.browserMode": "Select browser mode", "autoRegister.showBrowser": "🖥️ Show Browser Window", "autoRegister.showBrowserDesc": "Can see the operation process", "autoRegister.showBrowserDetail": "Recommended: Easy to debug and user interaction", "autoRegister.headless": "🔇 Background Mode", "autoRegister.headlessDesc": "Hide browser window", "autoRegister.headlessDetail": "Advanced: Faster but cannot see process", "autoRegister.loginMethod": "Select login/registration method", "autoRegister.emailRegister": "📧 Email Registration", "autoRegister.emailRegisterDesc": "Register new account with email address", "autoRegister.emailRegisterDetail": "Traditional way: Enter email → Verification code → Complete registration", "autoRegister.microsoftLogin": "🔷 Microsoft Account Login", "autoRegister.microsoftLoginDesc": "Quick login with Microsoft account", "autoRegister.microsoftLoginDetail": "Recommended: Faster and more secure login method", "autoRegister.success": "🎉 Auto registration successful!", "autoRegister.failed": "❌ Auto registration failed", "autoRegister.error": "❌ Auto registration process error", "puppeteerLogin.title": "Puppeteer <PERSON><PERSON><PERSON>", "puppeteerLogin.emailLogin": "📧 <PERSON><PERSON>", "puppeteerLogin.emailLoginDesc": "Login with email and password", "puppeteerLogin.emailLoginDetail": "Manually enter email and password in browser", "puppeteerLogin.microsoftLogin": "🔷 Microsoft Account Login", "puppeteerLogin.microsoftLoginDesc": "Login with Microsoft account", "puppeteerLogin.microsoftLoginDetail": "Recommended: Auto-click Microsoft login button", "puppeteerLogin.smartWait": "⏰ Smart Wait", "puppeteerLogin.smartWaitDesc": "Auto-detect login completion", "puppeteerLogin.smartWaitDetail": "Recommended: Auto-detect login status changes", "puppeteerLogin.manualConfirm": "👤 Manual Confirmation", "puppeteerLogin.manualConfirmDesc": "User manually confirms login completion", "puppeteerLogin.manualConfirmDetail": "Safe: User controls login completion timing", "puppeteerLogin.success": "🎉 Browser login successful!", "puppeteerLogin.failed": "❌ Browser login failed", "puppeteerLogin.error": "❌ Browser login process error"}