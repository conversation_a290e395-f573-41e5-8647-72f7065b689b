# 自动注册功能测试指南

## 测试前准备

1. **确保依赖已安装**
   ```bash
   npm install
   npm run compile
   ```

2. **确保浏览器可用**
   - Chrome: `C:\Program Files\Google\Chrome\Application\chrome.exe`
   - Edge: `C:\Program Files\Microsoft\Edge\Application\msedge.exe`

3. **准备测试邮箱**
   - 使用真实邮箱地址
   - 确保能够接收验证码

## 测试步骤

### 1. 启动VSCode插件
- 按 `F5` 启动调试模式
- 或者安装.vsix文件进行测试

### 2. 执行自动注册命令
- 打开命令面板 (`Ctrl+Shift+P`)
- 搜索 "🤖 自动注册账户"
- 执行命令

### 3. 配置选项测试

#### 邮箱输入测试
- ✅ 输入有效邮箱: `<EMAIL>`
- ❌ 输入无效邮箱: `invalid-email`
- ❌ 空邮箱输入

#### 人机验证模式测试
- 🤖 智能等待模式
- 👤 手动交互模式  
- ⏸️ 手动验证模式

#### 浏览器模式测试
- 🖥️ 显示浏览器窗口
- 🔇 后台运行

### 4. 流程测试点

#### 浏览器启动
- ✅ 成功启动Chrome/Edge
- ❌ 浏览器未找到错误处理
- ✅ 页面正确加载

#### 页面导航
- ✅ 访问 https://app.augmentcode.com/
- ✅ 自动跳转到登录页面
- ✅ 检测到 login.augmentcode.com

#### 邮箱填写
- ✅ 找到 #username 输入框
- ✅ 成功填写邮箱地址
- ❌ 输入框未找到错误处理

#### 人机验证处理
- ✅ 检测reCAPTCHA存在
- ✅ 智能等待模式工作
- ✅ 手动模式用户交互
- ✅ 交互模式步骤确认

#### 验证码页面
- ✅ 跳转到验证码页面
- ✅ 检测到 passwordless-email-challenge
- ✅ 找到验证码输入框

#### 验证码输入
- ✅ 用户输入6位数字验证码
- ❌ 无效验证码格式检查
- ✅ 成功提交验证码

#### Cookie提取
- ✅ 成功跳转到dashboard
- ✅ 提取 _session cookie
- ✅ 提取 ajs_user_id cookie
- ✅ 格式化cookie字符串

#### 集成测试
- ✅ Cookie保存到VSCode配置
- ✅ API客户端设置cookie
- ✅ 立即验证认证状态
- ✅ 获取用户数据
- ✅ 更新状态栏显示

## 错误场景测试

### 网络错误
- 断网情况下的错误处理
- 页面加载超时处理
- API调用失败处理

### 用户取消
- 各个步骤的取消操作
- 浏览器意外关闭
- 进度中断处理

### 验证失败
- reCAPTCHA验证失败
- 验证码错误或过期
- 邮箱不存在或无效

### 浏览器问题
- 浏览器未安装
- 浏览器启动失败
- 页面加载异常

## 预期结果

### 成功场景
```
🎉 自动注册成功！

邮箱: <EMAIL>
认证已配置，可以开始使用Augment服务。
```

### 失败场景
```
❌ 自动注册失败

错误: [具体错误信息]

请尝试手动注册或使用现有账户登录。
```

## 调试信息

查看VSCode开发者控制台的日志：
- `🤖 [AutoRegistration] Starting auto registration process...`
- `✅ [AutoRegistration] User options: {...}`
- `❌ [AutoRegistration] Registration process failed: ...`

## 性能指标

- 整个流程预期时间: 2-5分钟
- 浏览器启动时间: 5-10秒
- 页面加载时间: 3-8秒
- 人机验证时间: 30秒-2分钟（用户操作）
- Cookie提取时间: 1-3秒

## 已知限制

1. 需要用户手动完成reCAPTCHA
2. 需要用户手动输入验证码
3. 依赖Chrome/Edge浏览器
4. 网络连接要求稳定
5. 邮箱必须能接收验证码

## 后续优化计划

1. 支持更多浏览器类型
2. 改进错误恢复机制
3. 添加重试逻辑
4. 优化用户体验流程
5. 集成临时邮箱服务
