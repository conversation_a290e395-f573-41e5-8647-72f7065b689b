# Augment 自动注册功能使用指南

## 功能概述

新增的自动注册功能允许用户通过VSCode插件自动化完成Augment账户的注册流程，包括：

- 自动访问注册页面
- 自动填写邮箱地址
- 智能处理人机验证（reCAPTCHA）
- 自动输入验证码
- 自动提取认证Cookie

## 使用方法

### 1. 启动自动注册

在VSCode中打开命令面板（Ctrl+Shift+P），搜索并执行：
```
🤖 自动注册账户
```

### 2. 配置选项

#### 邮箱地址
- 输入有效的邮箱地址用于注册
- 暂时需要手动输入，后期将支持自动生成

#### 人机验证处理方式
选择以下三种模式之一：

**🤖 智能等待模式（推荐）**
- 自动监控页面变化
- 检测到验证完成后自动继续
- 适合大多数用户

**👤 手动交互模式**
- 在关键步骤提示用户确认
- 每个步骤都会等待用户确认
- 最安全的选项

**⏸️ 手动验证模式**
- 检测到验证时暂停
- 等待用户手动完成后继续
- 最简单的选项

#### 浏览器运行模式

**🖥️ 显示浏览器窗口（推荐）**
- 可以看到整个操作过程
- 便于调试和用户交互
- 适合首次使用

**🔇 后台运行**
- 隐藏浏览器窗口
- 运行更快但无法看到过程
- 适合熟练用户

### 3. 注册流程

1. **初始化浏览器** - 启动Chrome/Edge浏览器
2. **访问注册页面** - 自动跳转到Augment登录页面
3. **填写邮箱** - 自动输入提供的邮箱地址
4. **处理人机验证** - 根据选择的模式处理reCAPTCHA
5. **等待验证码页面** - 自动跳转到验证码输入页面
6. **输入验证码** - 手动输入邮箱收到的6位验证码
7. **提取认证信息** - 自动获取session cookie
8. **完成注册** - 自动配置VSCode插件认证

## 注意事项

### 系统要求
- Windows 10/11
- 已安装Chrome或Edge浏览器
- 稳定的网络连接

### 人机验证处理
- reCAPTCHA需要用户手动完成
- 智能等待模式会自动检测完成状态
- 如果检测失败，可以选择手动模式

### 验证码输入
- 验证码通过邮箱接收
- 必须是6位数字
- 有时间限制，请及时输入

### 错误处理
- 如果注册失败，会显示详细错误信息
- 可以重新尝试或使用手动注册
- 浏览器会自动清理，无需手动关闭

## 故障排除

### 浏览器启动失败
- 确保已安装Chrome或Edge
- 检查浏览器是否被安全软件阻止
- 尝试以管理员权限运行VSCode

### 人机验证超时
- 切换到手动验证模式
- 确保网络连接稳定
- 尝试刷新页面重新验证

### 验证码输入失败
- 检查邮箱是否收到验证码
- 确保输入的是6位数字
- 验证码有时效性，过期需重新获取

### Cookie提取失败
- 确保完成了整个注册流程
- 检查是否成功跳转到dashboard
- 可以尝试手动登录后使用现有的Cookie配置功能

## 后续计划

- **自动邮箱生成**：支持指定邮箱后缀自动生成临时邮箱
- **验证码自动获取**：通过临时邮箱API自动获取验证码
- **批量注册**：支持一次性注册多个账户
- **更多浏览器支持**：支持Firefox等其他浏览器

## 技术实现

本功能基于以下技术：
- **Puppeteer**：浏览器自动化控制
- **VSCode API**：用户交互和进度显示
- **TypeScript**：类型安全的代码实现
- **多语言支持**：中英文界面

如有问题或建议，请通过GitHub Issues反馈。
