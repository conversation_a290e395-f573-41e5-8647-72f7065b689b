{"displayName": "Augment 使用量追踪器", "description": "在VSCode状态栏追踪和显示Augment AI使用统计", "command.resetUsage": "重置使用统计", "command.openSettings": "打开设置", "command.showDetails": "显示使用详情", "command.setupCookies": "设置浏览器Cookie", "command.checkAuthStatus": "检查认证状态", "command.webLogin": "🌐 网页自动登录", "command.manualRefresh": "🔄 手动刷新", "command.setLanguage": "🌐 设置语言", "command.checkCookieStatus": "🍪 检查Cookie状态", "command.refreshCookie": "🔄 刷新Cookie", "command.logout": "🚪 退出登录", "command.autoRegister": "🤖 自动注册账户", "command.puppeteerLogin": "🎭 浏览器登录并提取Cookie", "config.title": "Augment 使用量追踪器", "config.enabled": "启用/禁用 Augment 使用量追踪器", "config.usageLimit": "月度使用限额", "config.refreshInterval": "状态栏刷新间隔（秒）", "config.showInStatusBar": "在状态栏显示使用统计", "config.cookies": "Augment 浏览器会话Cookie", "config.language": "界面语言设置", "config.language.auto": "自动（跟随VSCode）", "config.language.en": "英文", "config.language.zhCn": "简体中文", "status.noAuth": "没有可用的认证信息获取真实数据", "status.fetchingData": "正在获取真实使用数据...", "status.apiSuccess": "API连接成功！", "status.apiFailed": "API连接失败", "status.cookiesConfigured": "🍪 Augment cookies 配置成功！", "status.checkingAuth": "🔍 检查认证状态...", "status.authStatus": "🔐 认证状态:", "status.apiToken": "API Token", "status.browserCookies": "Browser Cookies", "status.configured": "✅ 已配置", "status.notConfigured": "❌ 未配置", "status.connectionTest": "连接测试", "status.success": "✅ 成功", "status.failed": "❌ 失败", "status.error": "错误", "status.suggestion": "💡 建议: <PERSON><PERSON>可能已过期，请重新获取", "status.pleaseConfigureAuth": "💡 请先配置认证信息", "dialog.browserOpened": "🌐 浏览器已打开！请登录Augment，然后使用\"设置浏览器Cookie\"命令。只需要获取_session cookie即可。格式_session=......", "dialog.setupCookies": "设置<PERSON><PERSON>", "dialog.cancel": "取消", "dialog.webLoginError": "❌ 网页登录错误", "usage.currentUsage": "当前使用量", "usage.monthlyLimit": "月度限额", "usage.usagePercentage": "使用百分比", "usage.remaining": "剩余", "usage.lastReset": "上次重置", "usage.resetUsage": "重置使用量", "usage.openSettings": "打开设置", "tooltip.augmentUsageTracker": "Augment 使用量追踪器", "tooltip.current": "当前", "tooltip.limit": "限额", "tooltip.usage": "使用量", "tooltip.remaining": "剩余", "tooltip.plan": "计划", "tooltip.dataSource": "数据源", "tooltip.realDataFromApi": "来自Augment API的真实数据", "tooltip.simulatedData": "模拟数据", "autoRegister.title": "自动注册 Augment 账户", "autoRegister.emailPrompt": "请输入注册邮箱地址", "autoRegister.emailPlaceholder": "<EMAIL>", "autoRegister.emailValidation": "请输入有效的邮箱地址", "autoRegister.captchaMode": "选择人机验证处理方式", "autoRegister.smartWait": "🤖 智能等待模式", "autoRegister.smartWaitDesc": "自动检测验证完成状态", "autoRegister.smartWaitDetail": "推荐：自动监控页面变化，检测到验证完成后继续", "autoRegister.interactive": "👤 手动交互模式", "autoRegister.interactiveDesc": "在关键步骤提示用户确认", "autoRegister.interactiveDetail": "安全：每个步骤都会提示用户确认后继续", "autoRegister.manual": "⏸️ 手动验证模式", "autoRegister.manualDesc": "暂停等待用户手动完成", "autoRegister.manualDetail": "简单：检测到验证时暂停，等待用户完成后继续", "autoRegister.browserMode": "选择浏览器运行模式", "autoRegister.showBrowser": "🖥️ 显示浏览器窗口", "autoRegister.showBrowserDesc": "可以看到操作过程", "autoRegister.showBrowserDetail": "推荐：便于调试和用户交互", "autoRegister.headless": "🔇 后台运行", "autoRegister.headlessDesc": "隐藏浏览器窗口", "autoRegister.headlessDetail": "高级：更快但无法看到过程", "autoRegister.loginMethod": "选择登录/注册方式", "autoRegister.emailRegister": "📧 邮箱注册", "autoRegister.emailRegisterDesc": "使用邮箱地址注册新账户", "autoRegister.emailRegisterDetail": "传统方式：输入邮箱 → 验证码 → 完成注册", "autoRegister.microsoftLogin": "🔷 微软账号登录", "autoRegister.microsoftLoginDesc": "使用微软账号快速登录", "autoRegister.microsoftLoginDetail": "推荐：更快速、更安全的登录方式", "autoRegister.success": "🎉 自动注册成功！", "autoRegister.failed": "❌ 自动注册失败", "autoRegister.error": "❌ 自动注册过程出错", "puppeteerLogin.title": "浏览器登录并提取Cookie", "puppeteerLogin.emailLogin": "📧 邮箱登录", "puppeteerLogin.emailLoginDesc": "使用邮箱和密码登录", "puppeteerLogin.emailLoginDetail": "在浏览器中手动输入邮箱和密码", "puppeteerLogin.microsoftLogin": "🔷 微软账号登录", "puppeteerLogin.microsoftLoginDesc": "使用微软账号登录", "puppeteerLogin.microsoftLoginDetail": "推荐：自动点击微软登录按钮", "puppeteerLogin.smartWait": "⏰ 智能等待", "puppeteerLogin.smartWaitDesc": "自动检测登录完成", "puppeteerLogin.smartWaitDetail": "推荐：自动检测登录状态变化", "puppeteerLogin.manualConfirm": "👤 手动确认", "puppeteerLogin.manualConfirmDesc": "用户手动确认登录完成", "puppeteerLogin.manualConfirmDetail": "安全：用户控制登录完成时机", "puppeteerLogin.success": "🎉 浏览器登录成功！", "puppeteerLogin.failed": "❌ 浏览器登录失败", "puppeteerLogin.error": "❌ 浏览器登录过程出错"}